import {
  createSlice,
  createAsyncThunk,
  PayloadAction,
} from "@reduxjs/toolkit";
import { activityLogAdminApi } from "../../../services/mainApi/admin/activityLog.admin.mainApi";
import {
  AdminActivityLogEntry,
  AdminActivityLogListParams,
} from "../../../services/mainApi/admin/types/activityLog.admin.mainApi.types";
import { BasePaginationMeta } from "../../../services/mainApi/types/base.mainApi.types";
import { RootState } from "../../../store";
import { AdminActivity } from "../../../services/mainApi/admin/types/activity.admin.mainApi.types";
import { AdminRole } from "../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { AdminUser } from "../../../services/mainApi/admin/types/user.admin.mainApi.types";
import { AdminDevice } from "../../../services/mainApi/admin/types/device.admin.mainApi.types";
import { AdminLabel } from "../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminBranch } from "../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import dayjs from "dayjs";

interface State {
  activityLogs: AdminActivityLogEntry[];
  loading: boolean;
  error: string | null;

  filter: {
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
    branchId: string | number | null;
    roleId: string | number | null;
    userId: string | number | null;
    userLabels: (string | number)[];
    deviceId: string | number | null;
    deviceLabels: (string | number)[];
    activityId: string | number | null;
    page: number;
    limit: number;
    orderBy: string;
    orderDirection: "ASC" | "DESC";
  };
  pagination: BasePaginationMeta;

  // Filter selection objects
  selectedRole: AdminRole | null;
  selectedUser: AdminUser | null;
  selectedDevice: AdminDevice | null;
  selectedActivity: AdminActivity | null;
  selectedBranch: AdminBranch | null;
  selectedUserLabels: AdminLabel[];
  selectedDeviceLabels: AdminLabel[];
}

const initialState: State = {
  activityLogs: [],
  loading: false,
  error: null,

  filter: {
    startDate: dayjs().subtract(1, 'day').startOf('day').format('YYYY-MM-DD'),
    endDate: dayjs().endOf('day').format('YYYY-MM-DD'),
    startTime: "00:00:00",
    endTime: "23:59:59",
    branchId: null,
    roleId: null,
    userId: null,
    userLabels: [],
    deviceId: null,
    deviceLabels: [],
    activityId: null,
    page: 1,
    limit: 10,
    orderBy: "original_submitted_time",
    orderDirection: "DESC",
  },
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
  },

  selectedRole: null,
  selectedUser: null,
  selectedDevice: null,
  selectedActivity: null,
  selectedBranch: null,
  selectedUserLabels: [],
  selectedDeviceLabels: [],
};

export const fetchActivityLogs = createAsyncThunk(
  "activityLogAdmin/fetchActivityLogs",
  async (
    {
      branchCode,
    }: {
      branchCode: string;
    },
    { getState }
  ) => {
    const state = getState() as RootState;
    const activityLogAdmin = state.activityLogAdmin;

    const params: AdminActivityLogListParams = {
      page: activityLogAdmin.filter.page,
      limit: activityLogAdmin.filter.limit,
      start_date:
        activityLogAdmin.filter.startDate || undefined,
      end_date:
        activityLogAdmin.filter.endDate || undefined,
      start_time:
        activityLogAdmin.filter.startTime || undefined,
      end_time:
        activityLogAdmin.filter.endTime || undefined,
      branch_id:
        activityLogAdmin.filter.branchId || undefined,
      role_id: activityLogAdmin.filter.roleId || undefined,
      user_id: activityLogAdmin.filter.userId || undefined,
      user_labels:
        activityLogAdmin.filter.userLabels.length > 0
          ? activityLogAdmin.filter.userLabels.join(",")
          : undefined,
      device_id:
        activityLogAdmin.filter.deviceId || undefined,
      device_labels:
        activityLogAdmin.filter.deviceLabels.length > 0
          ? activityLogAdmin.filter.deviceLabels.join(",")
          : undefined,
      activity_id:
        activityLogAdmin.filter.activityId || undefined,
      order_by: activityLogAdmin.filter.orderBy,
      order_direction:
        activityLogAdmin.filter.orderDirection,
    };

    return await activityLogAdminApi.getActivityLogs(
      branchCode,
      params
    );
  }
);

const activityLogAdminSlice = createSlice({
  name: "activityLogAdmin",
  initialState,
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.filter.page = action.payload;
    },
    setLimit: (state, action: PayloadAction<number>) => {
      state.filter.limit = action.payload;
    },
    setOrderBy: (state, action: PayloadAction<string>) => {
      state.filter.orderBy = action.payload;
    },
    setOrderDirection: (
      state,
      action: PayloadAction<"ASC" | "DESC">
    ) => {
      state.filter.orderDirection = action.payload;
    },
    clearFilters: (state) => {
      state.filter = {
        ...initialState.filter,
        page: state.filter.page,
        limit: state.filter.limit,
      };
      state.selectedRole = null;
      state.selectedUser = null;
      state.selectedDevice = null;
      state.selectedActivity = null;
      state.selectedBranch = null;
      state.selectedUserLabels = [];
      state.selectedDeviceLabels = [];
    },

    // Setting filter selection objects
    setSelectedRole: (
      state,
      action: PayloadAction<AdminRole | null>
    ) => {
      state.selectedRole = action.payload;
      state.filter.roleId = action.payload?.id || null;
    },
    setSelectedUser: (
      state,
      action: PayloadAction<AdminUser | null>
    ) => {
      state.selectedUser = action.payload;
      state.filter.userId = action.payload?.id || null;
    },
    setSelectedDevice: (
      state,
      action: PayloadAction<AdminDevice | null>
    ) => {
      state.selectedDevice = action.payload;
      state.filter.deviceId = action.payload?.id || null;
    },
    setSelectedActivity: (
      state,
      action: PayloadAction<AdminActivity | null>
    ) => {
      state.selectedActivity = action.payload;
      state.filter.activityId = action.payload?.id || null;
    },
    setSelectedBranch: (
      state,
      action: PayloadAction<AdminBranch | null>
    ) => {
      state.selectedBranch = action.payload;
      state.filter.branchId = action.payload?.id || null;
    },
    setSelectedUserLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedUserLabels = action.payload;
      state.filter.userLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setSelectedDeviceLabels: (
      state,
      action: PayloadAction<AdminLabel[]>
    ) => {
      state.selectedDeviceLabels = action.payload;
      state.filter.deviceLabels = action.payload.map(
        (label: AdminLabel) => label.id
      );
    },
    setStartDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.startDate = action.payload;
    },
    setEndDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.endDate = action.payload;
    },
    setStartTime: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.startTime = action.payload;
    },
    setEndTime: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.endTime = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Activity Logs
      .addCase(fetchActivityLogs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchActivityLogs.fulfilled,
        (state, action) => {
          state.loading = false;
          state.activityLogs = action.payload.data || [];
          state.pagination.total =
            action.payload.meta?.total || 0;
          state.pagination.page =
            action.payload.meta?.page || 1;
          state.pagination.limit =
            action.payload.meta?.limit || 10;
        }
      )
      .addCase(
        fetchActivityLogs.rejected,
        (state, action) => {
          state.loading = false;
          state.error =
            action.error.message ||
            "Failed to fetch activity logs";
        }
      );
  },
});

export default activityLogAdminSlice;
