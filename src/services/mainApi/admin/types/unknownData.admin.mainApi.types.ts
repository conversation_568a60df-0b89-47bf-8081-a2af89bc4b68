import { BaseResponse } from "../../types/base.mainApi.types";

// Unknown Data Entry interface
export interface AdminUnknownDataEntry {
  id: string;
  uuid: string;
  parent_branch_id: string;
  role_id: string;
  role_name: string;
  user_id: string;
  user_name: string;
  device_id: string;
  device_name: string;
  timezone_id: string;
  timezone_name: string;
  latitude: number;
  longitude: number;
  major_value: string;
  minor_value: string;
  checkpoint_type_id: string;
  serial_number: string | null;
  original_submitted_time: string;
  event_time: string;
}

// Request parameters interface
export interface AdminUnknownDataListParams {
  start_date?: string; // Format: YYYY-MM-DD
  end_date?: string; // Format: YYYY-MM-DD
  start_time?: string; // Format: HH:mm
  end_time?: string; // Format: HH:mm
  limit?: number;
  order_by?: string;
  page?: number;
}

// Extended pagination metadata for unknown data response
interface UnknownDataPaginationMeta {
  timestamp: string;
  local_time: string;
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// Response types
export type AdminUnknownDataListResponse = BaseResponse<
  AdminUnknownDataEntry[],
  UnknownDataPaginationMeta
>;
